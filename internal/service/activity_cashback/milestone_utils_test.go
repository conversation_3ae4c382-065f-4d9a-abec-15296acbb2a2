package activity_cashback

import (
	"testing"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

func TestGetNextMilestone(t *testing.T) {
	milestones := []model.ConsecutiveCheckinMilestone{
		{Days: 3, Points: 50},
		{Days: 7, Points: 100},
		{Days: 30, Points: 500},
	}

	tests := []struct {
		name           string
		currentStreak  int
		expectedDays   int
		expectedPoints int
		shouldBeNil    bool
	}{
		{
			name:           "No streak - should get first milestone",
			currentStreak:  0,
			expectedDays:   3,
			expectedPoints: 50,
			shouldBeNil:    false,
		},
		{
			name:           "2 days streak - should get 3-day milestone",
			currentStreak:  2,
			expectedDays:   3,
			expectedPoints: 50,
			shouldBeNil:    false,
		},
		{
			name:           "3 days streak - should get 7-day milestone",
			currentStreak:  3,
			expectedDays:   7,
			expectedPoints: 100,
			shouldBeNil:    false,
		},
		{
			name:           "10 days streak - should get 30-day milestone",
			currentStreak:  10,
			expectedDays:   30,
			expectedPoints: 500,
			shouldBeNil:    false,
		},
		{
			name:           "30+ days streak - should get highest milestone",
			currentStreak:  35,
			expectedDays:   30,
			expectedPoints: 500,
			shouldBeNil:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetNextMilestone(milestones, tt.currentStreak)
			
			if tt.shouldBeNil {
				if result != nil {
					t.Errorf("Expected nil, got milestone with %d days", result.Days)
				}
			} else {
				if result == nil {
					t.Errorf("Expected milestone, got nil")
					return
				}
				if result.Days != tt.expectedDays {
					t.Errorf("Expected %d days, got %d days", tt.expectedDays, result.Days)
				}
				if result.Points != tt.expectedPoints {
					t.Errorf("Expected %d points, got %d points", tt.expectedPoints, result.Points)
				}
			}
		})
	}
}

func TestGetCurrentMilestone(t *testing.T) {
	milestones := []model.ConsecutiveCheckinMilestone{
		{Days: 3, Points: 50},
		{Days: 7, Points: 100},
		{Days: 30, Points: 500},
	}

	tests := []struct {
		name           string
		currentStreak  int
		expectedDays   int
		expectedPoints int
		shouldBeNil    bool
	}{
		{
			name:        "No streak - should be nil",
			currentStreak: 0,
			shouldBeNil: true,
		},
		{
			name:        "2 days streak - should be nil",
			currentStreak: 2,
			shouldBeNil: true,
		},
		{
			name:           "3 days streak - should get 3-day milestone",
			currentStreak:  3,
			expectedDays:   3,
			expectedPoints: 50,
			shouldBeNil:    false,
		},
		{
			name:           "7 days streak - should get 7-day milestone",
			currentStreak:  7,
			expectedDays:   7,
			expectedPoints: 100,
			shouldBeNil:    false,
		},
		{
			name:           "10 days streak - should get 7-day milestone",
			currentStreak:  10,
			expectedDays:   7,
			expectedPoints: 100,
			shouldBeNil:    false,
		},
		{
			name:           "30+ days streak - should get 30-day milestone",
			currentStreak:  35,
			expectedDays:   30,
			expectedPoints: 500,
			shouldBeNil:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetCurrentMilestone(milestones, tt.currentStreak)
			
			if tt.shouldBeNil {
				if result != nil {
					t.Errorf("Expected nil, got milestone with %d days", result.Days)
				}
			} else {
				if result == nil {
					t.Errorf("Expected milestone, got nil")
					return
				}
				if result.Days != tt.expectedDays {
					t.Errorf("Expected %d days, got %d days", tt.expectedDays, result.Days)
				}
				if result.Points != tt.expectedPoints {
					t.Errorf("Expected %d points, got %d points", tt.expectedPoints, result.Points)
				}
			}
		})
	}
}

func TestGetMilestoneDisplayInfo(t *testing.T) {
	customName := &model.MultilingualName{
		En: "Three Day Reward",
		Zh: &[]string{"三天奖励"}[0],
		Vi: &[]string{"Phần thưởng 3 ngày"}[0],
	}

	milestones := []model.ConsecutiveCheckinMilestone{
		{Days: 3, Points: 50, Name: customName},
		{Days: 7, Points: 100},
		{Days: 30, Points: 500},
	}

	tests := []struct {
		name                string
		currentStreak       int
		preferredLang       string
		expectedDisplayName string
		expectedProgress    int
		expectedCompleted   bool
	}{
		{
			name:                "No streak - should show custom name for 3-day milestone",
			currentStreak:       0,
			preferredLang:       "en",
			expectedDisplayName: "Three Day Reward",
			expectedProgress:    0,
			expectedCompleted:   false,
		},
		{
			name:                "No streak - should show Chinese custom name",
			currentStreak:       0,
			preferredLang:       "zh",
			expectedDisplayName: "三天奖励",
			expectedProgress:    0,
			expectedCompleted:   false,
		},
		{
			name:                "2 days streak - should show custom name for 3-day milestone",
			currentStreak:       2,
			preferredLang:       "en",
			expectedDisplayName: "Three Day Reward",
			expectedProgress:    2,
			expectedCompleted:   false,
		},
		{
			name:                "5 days streak - should show default name for 7-day milestone",
			currentStreak:       5,
			preferredLang:       "en",
			expectedDisplayName: "7-Day Consecutive Check-in",
			expectedProgress:    5,
			expectedCompleted:   false,
		},
		{
			name:                "35 days streak - all completed",
			currentStreak:       35,
			preferredLang:       "en",
			expectedDisplayName: "30-Day Consecutive Check-in",
			expectedProgress:    35,
			expectedCompleted:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetMilestoneDisplayInfo(milestones, tt.currentStreak, tt.preferredLang)
			
			if result == nil {
				t.Errorf("Expected milestone display info, got nil")
				return
			}

			if result.DisplayName != tt.expectedDisplayName {
				t.Errorf("Expected display name '%s', got '%s'", tt.expectedDisplayName, result.DisplayName)
			}

			if result.Progress != tt.expectedProgress {
				t.Errorf("Expected progress %d, got %d", tt.expectedProgress, result.Progress)
			}

			if result.IsCompleted != tt.expectedCompleted {
				t.Errorf("Expected completed %v, got %v", tt.expectedCompleted, result.IsCompleted)
			}
		})
	}
}

func TestGetMilestoneProgress(t *testing.T) {
	milestones := []model.ConsecutiveCheckinMilestone{
		{Days: 3, Points: 50},
		{Days: 7, Points: 100},
		{Days: 30, Points: 500},
	}

	tests := []struct {
		name               string
		currentStreak      int
		expectedCurrent    int
		expectedTotal      int
		expectedPercentage float64
	}{
		{
			name:               "No streak",
			currentStreak:      0,
			expectedCurrent:    0,
			expectedTotal:      3,
			expectedPercentage: 0.0,
		},
		{
			name:               "2 days streak",
			currentStreak:      2,
			expectedCurrent:    2,
			expectedTotal:      3,
			expectedPercentage: 66.7,
		},
		{
			name:               "5 days streak",
			currentStreak:      5,
			expectedCurrent:    5,
			expectedTotal:      7,
			expectedPercentage: 71.4,
		},
		{
			name:               "35 days streak - all completed",
			currentStreak:      35,
			expectedCurrent:    30,
			expectedTotal:      30,
			expectedPercentage: 100.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			current, total, percentage := GetMilestoneProgress(milestones, tt.currentStreak)
			
			if current != tt.expectedCurrent {
				t.Errorf("Expected current %d, got %d", tt.expectedCurrent, current)
			}

			if total != tt.expectedTotal {
				t.Errorf("Expected total %d, got %d", tt.expectedTotal, total)
			}

			// Allow small floating point differences
			if percentage < tt.expectedPercentage-0.1 || percentage > tt.expectedPercentage+0.1 {
				t.Errorf("Expected percentage %.1f, got %.1f", tt.expectedPercentage, percentage)
			}
		})
	}
}
