package activity_cashback

import (
	"testing"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

func TestMilestoneProgressTracking(t *testing.T) {
	// Create a mock task progress service
	service := &TaskProgressService{}

	// Create test milestones
	milestones := []model.ConsecutiveCheckinMilestone{
		{Days: 2, Points: 50, Name: &model.MultilingualName{En: "Daily 2"}},
		{Days: 3, Points: 200, Name: &model.MultilingualName{En: "Daily 3"}},
		{Days: 4, Points: 200, Name: &model.MultilingualName{En: "Daily 4"}},
	}

	// Test getFirstMilestone
	firstMilestone := service.getFirstMilestone(milestones)
	if firstMilestone == nil {
		t.Fatal("Expected first milestone, got nil")
	}
	if firstMilestone.Days != 2 {
		t.<PERSON><PERSON>rf("Expected first milestone to be 2 days, got %d", firstMilestone.Days)
	}
	if firstMilestone.Points != 50 {
		t.<PERSON><PERSON><PERSON>("Expected first milestone to be 50 points, got %d", firstMilestone.Points)
	}

	// Test getNextMilestone with different streak counts
	testCases := []struct {
		name           string
		currentStreak  int
		expectedDays   int
		expectedPoints int
		shouldBeNil    bool
	}{
		{
			name:           "No streak - should get 2-day milestone",
			currentStreak:  0,
			expectedDays:   2,
			expectedPoints: 50,
			shouldBeNil:    false,
		},
		{
			name:           "1 day streak - should get 2-day milestone",
			currentStreak:  1,
			expectedDays:   2,
			expectedPoints: 50,
			shouldBeNil:    false,
		},
		{
			name:           "2 day streak - should get 3-day milestone",
			currentStreak:  2,
			expectedDays:   3,
			expectedPoints: 200,
			shouldBeNil:    false,
		},
		{
			name:           "3 day streak - should get 4-day milestone",
			currentStreak:  3,
			expectedDays:   4,
			expectedPoints: 200,
			shouldBeNil:    false,
		},
		{
			name:          "4+ day streak - all milestones completed",
			currentStreak: 4,
			shouldBeNil:   true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			nextMilestone := service.getNextMilestone(milestones, tc.currentStreak)

			if tc.shouldBeNil {
				if nextMilestone != nil {
					t.Errorf("Expected nil, got milestone with %d days", nextMilestone.Days)
				}
			} else {
				if nextMilestone == nil {
					t.Errorf("Expected milestone, got nil")
					return
				}
				if nextMilestone.Days != tc.expectedDays {
					t.Errorf("Expected %d days, got %d days", tc.expectedDays, nextMilestone.Days)
				}
				if nextMilestone.Points != tc.expectedPoints {
					t.Errorf("Expected %d points, got %d points", tc.expectedPoints, nextMilestone.Points)
				}
			}
		})
	}
}

func TestProgressValueAndTargetValueLogic(t *testing.T) {
	// Test the expected behavior for progress tracking
	testCases := []struct {
		name                    string
		currentStreak           int
		milestones              []model.ConsecutiveCheckinMilestone
		expectedProgressValue   int
		expectedTargetValue     int
		expectedCompletionCount int
		expectedPointsEarned    int
	}{
		{
			name:          "Initial state - no check-ins",
			currentStreak: 0,
			milestones: []model.ConsecutiveCheckinMilestone{
				{Days: 2, Points: 50},
				{Days: 3, Points: 200},
				{Days: 4, Points: 200},
			},
			expectedProgressValue:   0,
			expectedTargetValue:     2, // First milestone
			expectedCompletionCount: 0,
			expectedPointsEarned:    0,
		},
		{
			name:          "After 1 check-in",
			currentStreak: 1,
			milestones: []model.ConsecutiveCheckinMilestone{
				{Days: 2, Points: 50},
				{Days: 3, Points: 200},
				{Days: 4, Points: 200},
			},
			expectedProgressValue:   1,
			expectedTargetValue:     2, // Still working towards first milestone
			expectedCompletionCount: 0,
			expectedPointsEarned:    0,
		},
		{
			name:          "After 2 check-ins - first milestone reached",
			currentStreak: 2,
			milestones: []model.ConsecutiveCheckinMilestone{
				{Days: 2, Points: 50},
				{Days: 3, Points: 200},
				{Days: 4, Points: 200},
			},
			expectedProgressValue:   2,
			expectedTargetValue:     3, // Now working towards second milestone
			expectedCompletionCount: 1, // First milestone completed
			expectedPointsEarned:    50,
		},
		{
			name:          "After 3 check-ins - second milestone reached",
			currentStreak: 3,
			milestones: []model.ConsecutiveCheckinMilestone{
				{Days: 2, Points: 50},
				{Days: 3, Points: 200},
				{Days: 4, Points: 200},
			},
			expectedProgressValue:   3,
			expectedTargetValue:     4,   // Now working towards third milestone
			expectedCompletionCount: 2,   // Two milestones completed
			expectedPointsEarned:    250, // 50 + 200
		},
		{
			name:          "After 4 check-ins - all milestones completed",
			currentStreak: 4,
			milestones: []model.ConsecutiveCheckinMilestone{
				{Days: 2, Points: 50},
				{Days: 3, Points: 200},
				{Days: 4, Points: 200},
			},
			expectedProgressValue:   4,
			expectedTargetValue:     4,   // Target stays at highest milestone
			expectedCompletionCount: 3,   // All milestones completed
			expectedPointsEarned:    450, // 50 + 200 + 200
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Test progress value logic
			if tc.currentStreak != tc.expectedProgressValue {
				t.Errorf("Progress value should equal streak count. Expected %d, got %d",
					tc.expectedProgressValue, tc.currentStreak)
			}

			// Test target value logic
			service := &TaskProgressService{}
			nextMilestone := service.getNextMilestone(tc.milestones, tc.currentStreak)
			var actualTargetValue int
			if nextMilestone != nil {
				actualTargetValue = nextMilestone.Days
			} else {
				// All milestones completed, target should be highest milestone
				highestMilestone := service.getHighestMilestone(tc.milestones)
				if highestMilestone != nil {
					actualTargetValue = highestMilestone.Days
				}
			}

			if actualTargetValue != tc.expectedTargetValue {
				t.Errorf("Expected target value %d, got %d", tc.expectedTargetValue, actualTargetValue)
			}

			// Note: completionCount and pointsEarned would be tested in integration tests
			// as they require actual milestone completion logic
		})
	}
}

func TestResetLogic(t *testing.T) {
	// Test that when streak resets, progress values reset correctly
	testCases := []struct {
		name                         string
		beforeStreakCount            int
		afterStreakCount             int
		milestones                   []model.ConsecutiveCheckinMilestone
		expectedProgressValue        int
		expectedTargetValue          int
		completionCountShouldPersist bool
		pointsEarnedShouldPersist    bool
	}{
		{
			name:              "Reset from 3-day streak to 0",
			beforeStreakCount: 3,
			afterStreakCount:  0,
			milestones: []model.ConsecutiveCheckinMilestone{
				{Days: 2, Points: 50},
				{Days: 3, Points: 200},
				{Days: 4, Points: 200},
			},
			expectedProgressValue:        0,
			expectedTargetValue:          2,    // Back to first milestone
			completionCountShouldPersist: true, // Lifetime achievements persist
			pointsEarnedShouldPersist:    true, // Lifetime points persist
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			service := &TaskProgressService{}

			// Test that progress value resets with streak
			if tc.afterStreakCount != tc.expectedProgressValue {
				t.Errorf("After reset, progress value should equal streak count. Expected %d, got %d",
					tc.expectedProgressValue, tc.afterStreakCount)
			}

			// Test that target value resets to first milestone
			nextMilestone := service.getNextMilestone(tc.milestones, tc.afterStreakCount)
			var actualTargetValue int
			if nextMilestone != nil {
				actualTargetValue = nextMilestone.Days
			}

			if actualTargetValue != tc.expectedTargetValue {
				t.Errorf("After reset, expected target value %d, got %d", tc.expectedTargetValue, actualTargetValue)
			}

			// Note: Persistence of completionCount and pointsEarned would be tested in integration tests
		})
	}
}
