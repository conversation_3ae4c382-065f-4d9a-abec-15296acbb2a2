package activity_cashback

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// MigrationVerificationTestSuite tests all functionality before and after migration
type MigrationVerificationTestSuite struct {
	suite.Suite
	ctx              context.Context
	service          ActivityCashbackServiceInterface
	processorManager *TaskManager
	testUserID       uuid.UUID
}

func TestMigrationVerificationSuite(t *testing.T) {
	suite.Run(t, new(MigrationVerificationTestSuite))
}

func (suite *MigrationVerificationTestSuite) SetupSuite() {
	suite.ctx = context.Background()
	suite.testUserID = uuid.New()

	// Initialize services (using in-memory/mock setup for testing)
	suite.service = NewActivityCashbackService()
	suite.processorManager = NewTaskManager(suite.service)
}

func (suite *MigrationVerificationTestSuite) TearDownSuite() {
	// Cleanup if needed
}

// TestProcessTradingEventFunctionality tests the core ProcessTradingEvent functionality
func (suite *MigrationVerificationTestSuite) TestProcessTradingEventFunctionality() {
	testCases := []struct {
		name      string
		tradeData map[string]interface{}
		expectErr bool
	}{
		{
			name: "Valid MEME Trade",
			tradeData: map[string]interface{}{
				"trade_type": "MEME",
				"volume":     1000.0,
				"symbol":     "TEST/USDT",
			},
			expectErr: false,
		},
		{
			name: "Valid PERPETUAL Trade",
			tradeData: map[string]interface{}{
				"trade_type": "PERPETUAL",
				"volume":     500.0,
				"symbol":     "BTC-USD",
			},
			expectErr: false,
		},
		{
			name: "Invalid Trade Type",
			tradeData: map[string]interface{}{
				"trade_type": "INVALID",
				"volume":     100.0,
			},
			expectErr: false, // Should skip, not error
		},
		{
			name: "Missing Trade Type",
			tradeData: map[string]interface{}{
				"volume": 100.0,
			},
			expectErr: true,
		},
		{
			name: "Invalid Volume",
			tradeData: map[string]interface{}{
				"trade_type": "MEME",
				"volume":     -100.0,
			},
			expectErr: true,
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			err := suite.processorManager.ProcessTradingEvent(suite.ctx, suite.testUserID, tc.tradeData)
			if tc.expectErr {
				suite.Error(err, "Expected error for test case: %s", tc.name)
			} else {
				suite.NoError(err, "Unexpected error for test case: %s", tc.name)
			}
		})
	}
}

// TestProcessTaskByIdentifierFunctionality tests ProcessTaskByIdentifier
func (suite *MigrationVerificationTestSuite) TestProcessTaskByIdentifierFunctionality() {
	testCases := []struct {
		name         string
		identifier   model.TaskIdentifier
		categoryName model.TaskCategoryName
		data         map[string]interface{}
		expectErr    bool
	}{
		{
			name:         "Daily Checkin",
			identifier:   model.TaskIDDailyCheckin,
			categoryName: "daily",
			data:         map[string]interface{}{},
			expectErr:    false,
		},
		{
			name:         "Trading Points",
			identifier:   model.TaskIDTradingPoints,
			categoryName: "trading",
			data: map[string]interface{}{
				"trade_type": "MEME",
				"volume":     100.0,
			},
			expectErr: false,
		},
		{
			name:         "Invalid Identifier",
			identifier:   "INVALID_TASK",
			categoryName: "daily",
			data:         map[string]interface{}{},
			expectErr:    true,
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			err := suite.processorManager.ProcessTaskByIdentifier(suite.ctx, suite.testUserID, tc.identifier, tc.categoryName, tc.data)
			if tc.expectErr {
				suite.Error(err, "Expected error for test case: %s", tc.name)
			} else {
				suite.NoError(err, "Unexpected error for test case: %s", tc.name)
			}
		})
	}
}

// TestTradingPointsCalculation tests the specific Trading Points calculation logic
func (suite *MigrationVerificationTestSuite) TestTradingPointsCalculation() {
	testCases := []struct {
		name           string
		tradeType      string
		volume         float64
		expectedPoints int
	}{
		{
			name:           "MEME Trade - Tier Based",
			tradeType:      "MEME",
			volume:         299.79, // Should give tier-based points
			expectedPoints: 1,      // Based on tier system
		},
		{
			name:           "PERPETUAL Trade - Direct",
			tradeType:      "PERPETUAL",
			volume:         299.79, // Should give direct points
			expectedPoints: 3,      // int(299.79/95) = 3
		},
		{
			name:           "Large PERPETUAL Trade",
			tradeType:      "PERPETUAL",
			volume:         950.0,
			expectedPoints: 10, // int(950/95) = 10
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			tradeData := map[string]interface{}{
				"trade_type": tc.tradeType,
				"volume":     tc.volume,
				"symbol":     "TEST/USDT",
			}

			// Get initial points
			initialTierInfo, err := suite.service.GetUserTierInfo(suite.ctx, suite.testUserID)
			initialPoints := 0
			if err == nil && initialTierInfo != nil {
				initialPoints = initialTierInfo.TotalPoints
			}

			// Process the trade
			err = suite.processorManager.ProcessTradingEvent(suite.ctx, suite.testUserID, tradeData)
			suite.NoError(err, "Trade processing should succeed")

			// Verify points were awarded correctly
			finalTierInfo, err := suite.service.GetUserTierInfo(suite.ctx, suite.testUserID)
			if err == nil && finalTierInfo != nil {
				pointsAwarded := finalTierInfo.TotalPoints - initialPoints
				suite.Equal(tc.expectedPoints, pointsAwarded,
					"Points awarded should match expected for %s trade with volume %.2f",
					tc.tradeType, tc.volume)
			}
		})
	}
}

// TestCheckAndUpdateAllAccumulatedTradingTasks tests accumulated trading functionality
func (suite *MigrationVerificationTestSuite) TestCheckAndUpdateAllAccumulatedTradingTasks() {
	// This might error due to database setup, but we're testing the method exists and is callable
	suite.NotPanics(func() {
		suite.processorManager.CheckAndUpdateAllAccumulatedTradingTasks(suite.ctx, suite.testUserID)
	}, "CheckAndUpdateAllAccumulatedTradingTasks should not panic")
}

// TestProcessTaskFunctionality tests the general ProcessTask functionality
func (suite *MigrationVerificationTestSuite) TestProcessTaskFunctionality() {
	// Create a mock task for testing
	taskID := model.TaskIDDailyCheckin
	task := &model.ActivityTask{
		ID:             uuid.New(),
		Name:           "Test Task",
		TaskIdentifier: &taskID,
		Points:         10,
		IsActive:       true,
	}

	// This might error due to database setup, but we're testing the method exists and delegates properly
	suite.NotPanics(func() {
		suite.processorManager.ProcessTask(suite.ctx, suite.testUserID, task, map[string]interface{}{})
	}, "ProcessTask should not panic")
}

// TestConstructorFunctionality tests NewTaskProcessorManager
func (suite *MigrationVerificationTestSuite) TestConstructorFunctionality() {
	// Test that constructor works and creates proper structure
	manager := NewTaskProcessorManager(suite.service)
	suite.NotNil(manager, "TaskProcessorManager should be created")
	suite.NotNil(manager.registry, "TaskRegistry should be initialized")
	suite.NotNil(manager.service, "Service should be set")
	suite.NotEmpty(manager.processors, "Processors should be initialized")
}
