package task

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/nats-io/nats.go"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/contract"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/transaction"
	"go.uber.org/zap"
)

func ConsumeHyperLiquidTransactionEvent(msgs []*nats.Msg) error {
	logger := global.GVA_LOG

	hyperLiquidTransactionService := transaction.NewHyperLiquidTransactionService()
	contractCommissionService := contract.NewContractCommissionService()
	activityService := activity_cashback.NewActivityCashbackService()
	taskProcessorManager := activity_cashback.NewTaskManager(activityService)

	for _, m := range msgs {
		var events HyperLiquidTransactionEventBatch
		if err := json.Unmarshal(m.Data, &events); err != nil {
			logger.Error("failed to unmarshal: %v", zap.Error(err))
			continue
		}

		for _, event := range events.Items {
			transactionEvent := model.HyperLiquidTransaction{
				Cloid:         event.Cloid,
				UserID:        &event.UserID,
				WalletAddress: event.WalletAddress,
				Side:          event.Side,
				OrderType:     event.OrderType,
				Symbol:        event.Symbol,
				IsBuy:         event.IsBuy,
				Leverage:      event.Leverage,
				Margin:        event.Margin,
				IsMarket:      event.IsMarket,
				TriggerPx:     event.TriggerPx,
				Tpsl:          event.Tpsl,
				Tif:           event.Tif,
				Base:          event.Base,
				Quote:         event.Quote,
				Size:          event.Size,
				Price:         event.Price,
				AvgPrice:      event.AvgPrice,
				BuildFee:      event.BuildFee,
				TotalFee:      event.TotalFee,
				FeeBp:         event.FeeBp,
				BuildAddress:  event.BuildAddress,
				Status:        event.Status,
				OID:           &event.OID,
				CreatedAt:     event.CreatedAt,
				TotalSz:       event.TotalSz,
				Hash:          event.Hash,
				Asset:         event.Asset,
				Coin:          event.Coin,
				ReduceOnly:    event.ReduceOnly,
				Grouping:      event.Grouping,
				Operation:     event.Operation,
			}

			now := time.Now()
			transactionEvent.CreatedTime = &now
			existingTx, err := hyperLiquidTransactionService.FindHyperLiquidTransactionByCloid(context.Background(), event.Cloid)
			if err != nil {
				logger.Error("failed to find transaction by cloid",
					zap.String("cloid", event.Cloid),
					zap.Error(err),
				)
				continue
			}

			if existingTx != nil {
				err = hyperLiquidTransactionService.UpdateHyperLiquidTransactionByCloid(context.Background(), event.Cloid, &transactionEvent)
				if err != nil {
					logger.Error("failed to update hyperliquid transaction",
						zap.String("cloid", event.Cloid),
						zap.Error(err),
					)
					continue
				}
				logger.Info("updated hyperliquid transaction",
					zap.String("cloid", event.Cloid),
				)
			} else {
				err = hyperLiquidTransactionService.BulkInsertHyperLiquidTransactionsEvent(context.Background(), []model.HyperLiquidTransaction{transactionEvent})
				if err != nil {
					logger.Error("failed to create hyperliquid transaction",
						zap.String("cloid", event.Cloid),
						zap.Error(err),
					)
					continue
				}
				logger.Info("created hyperliquid transaction",
					zap.String("cloid", event.Cloid),
				)
			}

			if err := processContractCommission(context.Background(), &transactionEvent, contractCommissionService); err != nil {
				logger.Error("failed to process contract commission",
					zap.String("cloid", event.Cloid),
					zap.Error(err),
				)
				continue
			}

			// Process derivatives trade task completion for filled transactions
			if transactionEvent.Status != nil && *transactionEvent.Status == "filled" && transactionEvent.UserID != nil {
				logger.Info("Processing derivatives trade task completion",
					zap.String("cloid", event.Cloid),
					zap.String("user_id", transactionEvent.UserID.String()),
					zap.String("status", *transactionEvent.Status))

				if err := processDerivativesTradeTaskCompletion(context.Background(), &transactionEvent, taskProcessorManager); err != nil {
					logger.Error("failed to process derivatives trade task completion",
						zap.String("cloid", event.Cloid),
						zap.Error(err),
					)
					// Don't continue here as this is not critical for the main transaction processing
				}
			}
		}
	}

	return nil
}

func processContractCommission(ctx context.Context, hyperliquidTx *model.HyperLiquidTransaction, commissionService *contract.ContractCommissionService) error {
	global.GVA_LOG.Info("processing contract commission",
		zap.String("cloid", hyperliquidTx.Cloid),
	)
	if hyperliquidTx.Status == nil || *hyperliquidTx.Status != "filled" {
		return nil
	}

	if hyperliquidTx.UserID == nil || hyperliquidTx.BuildFee.IsZero() {
		return nil
	}

	err := commissionService.ProcessContractCommission(ctx, hyperliquidTx)
	if err != nil {
		return fmt.Errorf("failed to process contract commission: %w", err)
	}

	return nil
}

// processDerivativesTradeTaskCompletion processes derivatives trade task completion when hyperliquid transaction is filled
func processDerivativesTradeTaskCompletion(ctx context.Context, hyperliquidTx *model.HyperLiquidTransaction, taskProcessorManager *activity_cashback.TaskManager) error {
	if hyperliquidTx == nil || hyperliquidTx.UserID == nil {
		return nil
	}

	if hyperliquidTx.Status == nil || *hyperliquidTx.Status != "filled" {
		return nil
	}

	// Calculate volume from avg_price * size
	volume := float64(0)
	if hyperliquidTx.AvgPrice != nil && hyperliquidTx.Size != nil {
		volume = hyperliquidTx.AvgPrice.Mul(*hyperliquidTx.Size).InexactFloat64()
	}

	if volume <= 0 {
		global.GVA_LOG.Debug("Skipping derivatives trade task completion due to zero volume",
			zap.String("cloid", hyperliquidTx.Cloid),
			zap.String("user_id", hyperliquidTx.UserID.String()))
		return nil
	}

	// Prepare trade data for task processing
	tradeData := map[string]interface{}{
		"trade_type": "PERPETUAL",
		"volume":     volume,
		"cloid":      hyperliquidTx.Cloid,
		"user_id":    hyperliquidTx.UserID.String(),
	}

	global.GVA_LOG.Info("Starting PERPETUAL trade task processing from HyperLiquid event",
		zap.String("user_id", hyperliquidTx.UserID.String()),
		zap.String("cloid", hyperliquidTx.Cloid),
		zap.Float64("volume", volume),
		zap.String("trade_type", "PERPETUAL"),
		zap.String("processing_source", "hyperliquid_nats_event"),
		zap.Any("trade_data", tradeData))

	// Process derivatives trade task completion
	if err := taskProcessorManager.ProcessTradingEvent(ctx, *hyperliquidTx.UserID, tradeData); err != nil {
		return fmt.Errorf("failed to process derivatives trading event: %w", err)
	}

	global.GVA_LOG.Info("Derivatives trade task completion processed",
		zap.String("user_id", hyperliquidTx.UserID.String()),
		zap.String("cloid", hyperliquidTx.Cloid),
		zap.Float64("volume", volume))

	return nil
}
